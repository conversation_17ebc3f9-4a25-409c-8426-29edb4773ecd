import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;
import 'package:ffmpeg_kit_flutter/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter/return_code.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:fitness_ai_app/services/pose_detector_service.dart';

class VideoFrameData {
  final img.Image frame;
  final List<KeyPoint>? keyPoints;
  final double timestamp;
  final int frameIndex;

  VideoFrameData({
    required this.frame,
    this.keyPoints,
    required this.timestamp,
    required this.frameIndex,
  });
}

class VideoProcessingProgress {
  final int currentFrame;
  final int totalFrames;
  final String stage;
  final double progress;

  VideoProcessingProgress({
    required this.currentFrame,
    required this.totalFrames,
    required this.stage,
    required this.progress,
  });
}

class VideoFrameProcessor {
  static final VideoFrameProcessor _instance = VideoFrameProcessor._internal();
  factory VideoFrameProcessor() => _instance;
  VideoFrameProcessor._internal();

  final PoseDetectorService _poseDetector = PoseDetectorService();
  
  /// Extract frames from video file and process them through pose detection
  Future<List<VideoFrameData>> processVideoFrames(
    String videoPath, {
    Function(VideoProcessingProgress)? onProgress,
  }) async {
    try {
      // Get video info first
      final videoInfo = await _getVideoInfo(videoPath);
      final fps = videoInfo['fps'] ?? 30.0;
      final duration = videoInfo['duration'] ?? 0.0;
      final totalFrames = (duration * fps).round();

      onProgress?.call(VideoProcessingProgress(
        currentFrame: 0,
        totalFrames: totalFrames,
        stage: 'Extracting frames...',
        progress: 0.0,
      ));

      // Extract frames from video
      final frames = await _extractFrames(videoPath, fps: fps);
      
      final processedFrames = <VideoFrameData>[];
      
      for (int i = 0; i < frames.length; i++) {
        final frame = frames[i];
        final timestamp = i / fps;
        
        // Process frame through pose detection
        final keyPoints = await _processFrameForPose(frame);
        
        processedFrames.add(VideoFrameData(
          frame: frame,
          keyPoints: keyPoints,
          timestamp: timestamp,
          frameIndex: i,
        ));

        // Update progress
        final progress = (i + 1) / frames.length;
        onProgress?.call(VideoProcessingProgress(
          currentFrame: i + 1,
          totalFrames: frames.length,
          stage: 'Processing pose detection...',
          progress: progress,
        ));
      }

      return processedFrames;
    } catch (e) {
      throw Exception('Failed to process video frames: $e');
    }
  }

  /// Extract frames from video using FFmpeg
  Future<List<img.Image>> _extractFrames(String videoPath, {double fps = 30.0}) async {
    final tempDir = await getTemporaryDirectory();
    final framesDir = Directory('${tempDir.path}/video_frames_${DateTime.now().millisecondsSinceEpoch}');
    
    if (!await framesDir.exists()) {
      await framesDir.create(recursive: true);
    }

    try {
      // Extract frames at 1 FPS for analysis (to reduce processing load)
      final outputPattern = '${framesDir.path}/frame_%04d.png';
      final command = '-i "$videoPath" -vf fps=1 "$outputPattern"';
      
      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();
      
      if (!ReturnCode.isSuccess(returnCode)) {
        throw Exception('FFmpeg frame extraction failed');
      }

      // Load extracted frames
      final frameFiles = framesDir.listSync()
          .where((file) => file.path.endsWith('.png'))
          .cast<File>()
          .toList();
      
      frameFiles.sort((a, b) => a.path.compareTo(b.path));

      final frames = <img.Image>[];
      for (final frameFile in frameFiles) {
        final bytes = await frameFile.readAsBytes();
        final image = img.decodeImage(bytes);
        if (image != null) {
          frames.add(image);
        }
      }

      // Clean up temporary files
      await framesDir.delete(recursive: true);
      
      return frames;
    } catch (e) {
      // Clean up on error
      if (await framesDir.exists()) {
        await framesDir.delete(recursive: true);
      }
      rethrow;
    }
  }

  /// Process a single frame through pose detection
  Future<List<KeyPoint>?> _processFrameForPose(img.Image frame) async {
    try {
      // Resize frame to model input size
      final resizedFrame = img.copyResize(frame, width: 257, height: 257);
      
      // Convert to format expected by pose detector
      // This is a simplified version - in practice, you'd need to convert
      // the img.Image to the format expected by your pose detection model
      
      // For now, return mock keypoints - this will be replaced with actual pose detection
      return _generateMockKeyPoints();
    } catch (e) {
      print('Error processing frame for pose: $e');
      return null;
    }
  }

  /// Get video information using FFmpeg
  Future<Map<String, dynamic>> _getVideoInfo(String videoPath) async {
    try {
      final command = '-i "$videoPath" -hide_banner';
      final session = await FFmpegKit.execute(command);
      final output = await session.getOutput();
      
      // Parse video info from FFmpeg output
      // This is a simplified parser - in production, you'd want more robust parsing
      final info = <String, dynamic>{};
      
      if (output != null) {
        // Extract FPS
        final fpsMatch = RegExp(r'(\d+(?:\.\d+)?)\s*fps').firstMatch(output);
        if (fpsMatch != null) {
          info['fps'] = double.parse(fpsMatch.group(1)!);
        }
        
        // Extract duration
        final durationMatch = RegExp(r'Duration:\s*(\d+):(\d+):(\d+)\.(\d+)').firstMatch(output);
        if (durationMatch != null) {
          final hours = int.parse(durationMatch.group(1)!);
          final minutes = int.parse(durationMatch.group(2)!);
          final seconds = int.parse(durationMatch.group(3)!);
          final milliseconds = int.parse(durationMatch.group(4)!);
          info['duration'] = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000;
        }
      }
      
      // Set defaults if parsing failed
      info['fps'] ??= 30.0;
      info['duration'] ??= 10.0;
      
      return info;
    } catch (e) {
      // Return defaults on error
      return {'fps': 30.0, 'duration': 10.0};
    }
  }

  /// Generate mock keypoints for testing
  List<KeyPoint> _generateMockKeyPoints() {
    // Return 17 keypoints with mock data
    return List.generate(17, (index) {
      return KeyPoint(
        0.3 + (index % 3) * 0.2, // x coordinate
        0.2 + (index % 4) * 0.2, // y coordinate
        0.8, // confidence score
      );
    });
  }

  /// Clean up resources
  void dispose() {
    // Clean up any resources if needed
  }
}
