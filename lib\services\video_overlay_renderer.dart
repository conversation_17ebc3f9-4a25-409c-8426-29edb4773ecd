import 'dart:math';
import 'dart:typed_data';
import 'package:image/image.dart' as img;
import 'package:fitness_ai_app/services/pose_detector_service.dart';
import 'package:fitness_ai_app/services/squat_analysis_service.dart';

class OverlayConfig {
  final bool showPoseLandmarks;
  final bool showSkeleton;
  final bool showRepCounter;
  final bool showFormFeedback;
  final bool showAngles;
  final img.Color poseColor;
  final img.Color skeletonColor;
  final img.Color correctFormColor;
  final img.Color incorrectFormColor;
  final int fontSize;

  OverlayConfig({
    this.showPoseLandmarks = true,
    this.showSkeleton = true,
    this.showRepCounter = true,
    this.showFormFeedback = true,
    this.showAngles = true,
    this.poseColor = const img.Color(255, 255, 0), // Yellow
    this.skeletonColor = const img.Color(255, 0, 0), // Red
    this.correctFormColor = const img.Color(0, 255, 0), // Green
    this.incorrectFormColor = const img.Color(255, 0, 0), // Red
    this.fontSize = 24,
  });
}

class VideoOverlayRenderer {
  static final VideoOverlayRenderer _instance = VideoOverlayRenderer._internal();
  factory VideoOverlayRenderer() => _instance;
  VideoOverlayRenderer._internal();

  // Pose connections for drawing skeleton
  static const List<List<int>> poseConnections = [
    // Face
    [0, 1], [1, 3], [0, 2], [2, 4],
    // Body
    [5, 6], [5, 7], [7, 9], [6, 8], [8, 10],
    [5, 11], [6, 12], [11, 12],
    // Legs
    [11, 13], [13, 15], [12, 14], [14, 16]
  ];

  /// Render overlays on a single frame
  img.Image renderFrameOverlays(
    img.Image frame,
    List<KeyPoint>? keyPoints,
    SquatRepData? currentRep,
    int totalReps,
    int correctReps,
    double timestamp, {
    OverlayConfig? config,
  }) {
    config ??= OverlayConfig();
    
    // Create a copy of the frame to avoid modifying the original
    final overlayFrame = img.Image.from(frame);

    if (keyPoints != null && keyPoints.isNotEmpty) {
      // Draw pose landmarks
      if (config.showPoseLandmarks) {
        _drawPoseLandmarks(overlayFrame, keyPoints, config);
      }

      // Draw skeleton connections
      if (config.showSkeleton) {
        _drawSkeleton(overlayFrame, keyPoints, config);
      }

      // Draw angle indicators
      if (config.showAngles) {
        _drawAngleIndicators(overlayFrame, keyPoints, config);
      }
    }

    // Draw rep counter
    if (config.showRepCounter) {
      _drawRepCounter(overlayFrame, totalReps, correctReps, config);
    }

    // Draw current form feedback
    if (config.showFormFeedback && currentRep != null) {
      _drawFormFeedback(overlayFrame, currentRep, config);
    }

    // Draw timestamp
    _drawTimestamp(overlayFrame, timestamp, config);

    return overlayFrame;
  }

  /// Draw pose landmarks as circles
  void _drawPoseLandmarks(
    img.Image frame,
    List<KeyPoint> keyPoints,
    OverlayConfig config,
  ) {
    for (int i = 0; i < keyPoints.length; i++) {
      final point = keyPoints[i];
      
      if (point.score > 0.5) {
        final x = (point.x * frame.width).round();
        final y = (point.y * frame.height).round();
        
        // Draw circle for landmark
        _drawCircle(frame, x, y, 6, config.poseColor);
        
        // Draw smaller inner circle for better visibility
        _drawCircle(frame, x, y, 3, img.Color(255, 255, 255));
      }
    }
  }

  /// Draw skeleton connections between landmarks
  void _drawSkeleton(
    img.Image frame,
    List<KeyPoint> keyPoints,
    OverlayConfig config,
  ) {
    for (final connection in poseConnections) {
      if (connection[0] < keyPoints.length && connection[1] < keyPoints.length) {
        final point1 = keyPoints[connection[0]];
        final point2 = keyPoints[connection[1]];
        
        if (point1.score > 0.5 && point2.score > 0.5) {
          final x1 = (point1.x * frame.width).round();
          final y1 = (point1.y * frame.height).round();
          final x2 = (point2.x * frame.width).round();
          final y2 = (point2.y * frame.height).round();
          
          _drawLine(frame, x1, y1, x2, y2, config.skeletonColor, 3);
        }
      }
    }
  }

  /// Draw angle indicators for knee and hip
  void _drawAngleIndicators(
    img.Image frame,
    List<KeyPoint> keyPoints,
    OverlayConfig config,
  ) {
    if (keyPoints.length < 17) return;

    // Draw knee angle
    final hip = keyPoints[12]; // Right hip
    final knee = keyPoints[14]; // Right knee
    final ankle = keyPoints[16]; // Right ankle

    if (hip.score > 0.5 && knee.score > 0.5 && ankle.score > 0.5) {
      final angle = _calculateAngle(hip, knee, ankle);
      final kneeX = (knee.x * frame.width).round();
      final kneeY = (knee.y * frame.height).round();
      
      _drawText(frame, '${angle.round()}°', kneeX + 10, kneeY - 10, config);
    }
  }

  /// Draw repetition counter
  void _drawRepCounter(
    img.Image frame,
    int totalReps,
    int correctReps,
    OverlayConfig config,
  ) {
    final text = 'Reps: $totalReps | Correct: $correctReps';
    _drawText(frame, text, 20, 40, config);
  }

  /// Draw form feedback for current repetition
  void _drawFormFeedback(
    img.Image frame,
    SquatRepData currentRep,
    OverlayConfig config,
  ) {
    final color = currentRep.isCorrect ? config.correctFormColor : config.incorrectFormColor;
    final status = currentRep.isCorrect ? 'GOOD FORM' : 'CHECK FORM';
    
    // Draw form status
    _drawText(frame, status, 20, frame.height - 80, config, color: color);
    
    // Draw specific form issues
    if (currentRep.formIssues.isNotEmpty) {
      int yOffset = frame.height - 50;
      for (final issue in currentRep.formIssues) {
        final issueText = _getFormIssueText(issue);
        _drawText(frame, issueText, 20, yOffset, config, color: config.incorrectFormColor);
        yOffset += 25;
      }
    }
  }

  /// Draw timestamp on frame
  void _drawTimestamp(
    img.Image frame,
    double timestamp,
    OverlayConfig config,
  ) {
    final minutes = (timestamp / 60).floor();
    final seconds = (timestamp % 60).floor();
    final timeText = '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    
    _drawText(frame, timeText, frame.width - 100, 40, config);
  }

  /// Helper method to draw a circle
  void _drawCircle(img.Image frame, int centerX, int centerY, int radius, img.Color color) {
    for (int y = -radius; y <= radius; y++) {
      for (int x = -radius; x <= radius; x++) {
        if (x * x + y * y <= radius * radius) {
          final pixelX = centerX + x;
          final pixelY = centerY + y;
          
          if (pixelX >= 0 && pixelX < frame.width && pixelY >= 0 && pixelY < frame.height) {
            frame.setPixel(pixelX, pixelY, color);
          }
        }
      }
    }
  }

  /// Helper method to draw a line
  void _drawLine(img.Image frame, int x1, int y1, int x2, int y2, img.Color color, int thickness) {
    final dx = (x2 - x1).abs();
    final dy = (y2 - y1).abs();
    final sx = x1 < x2 ? 1 : -1;
    final sy = y1 < y2 ? 1 : -1;
    int err = dx - dy;

    int x = x1;
    int y = y1;

    while (true) {
      // Draw thick line by drawing multiple pixels
      for (int i = -thickness ~/ 2; i <= thickness ~/ 2; i++) {
        for (int j = -thickness ~/ 2; j <= thickness ~/ 2; j++) {
          final pixelX = x + i;
          final pixelY = y + j;
          
          if (pixelX >= 0 && pixelX < frame.width && pixelY >= 0 && pixelY < frame.height) {
            frame.setPixel(pixelX, pixelY, color);
          }
        }
      }

      if (x == x2 && y == y2) break;
      
      final e2 = 2 * err;
      if (e2 > -dy) {
        err -= dy;
        x += sx;
      }
      if (e2 < dx) {
        err += dx;
        y += sy;
      }
    }
  }

  /// Helper method to draw text (simplified implementation)
  void _drawText(
    img.Image frame,
    String text,
    int x,
    int y,
    OverlayConfig config, {
    img.Color? color,
  }) {
    color ??= img.Color(255, 255, 255);
    
    // This is a very basic text rendering - in production, you'd want to use
    // a proper font rendering library or pre-rendered text images
    img.drawString(frame, text, font: img.arial24, x: x, y: y, color: color);
  }

  /// Calculate angle between three points
  double _calculateAngle(KeyPoint p1, KeyPoint p2, KeyPoint p3) {
    final v1x = p1.x - p2.x;
    final v1y = p1.y - p2.y;
    final v2x = p3.x - p2.x;
    final v2y = p3.y - p2.y;

    final dot = v1x * v2x + v1y * v2y;
    final mag1 = sqrt(v1x * v1x + v1y * v1y);
    final mag2 = sqrt(v2x * v2x + v2y * v2y);

    if (mag1 == 0 || mag2 == 0) return 0.0;

    final cosAngle = dot / (mag1 * mag2);
    final angle = acos(cosAngle.clamp(-1.0, 1.0)) * 180 / pi;

    return angle;
  }

  /// Get human-readable text for form issues
  String _getFormIssueText(SquatFormIssue issue) {
    switch (issue) {
      case SquatFormIssue.kneeOverToe:
        return 'Knee over toe';
      case SquatFormIssue.insufficientDepth:
        return 'Go deeper';
      case SquatFormIssue.backRounding:
        return 'Keep back straight';
      case SquatFormIssue.kneeCollapse:
        return 'Keep knees out';
      case SquatFormIssue.heelLift:
        return 'Keep heels down';
      default:
        return 'Form issue';
    }
  }
}
