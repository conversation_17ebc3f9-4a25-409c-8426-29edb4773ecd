import 'dart:io';
import 'dart:typed_data';
import 'package:image/image.dart' as img;
import 'package:ffmpeg_kit_flutter/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter/return_code.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class VideoGenerationProgress {
  final int currentFrame;
  final int totalFrames;
  final String stage;
  final double progress;

  VideoGenerationProgress({
    required this.currentFrame,
    required this.totalFrames,
    required this.stage,
    required this.progress,
  });
}

class VideoOutputConfig {
  final int fps;
  final int width;
  final int height;
  final int bitrate;
  final String codec;
  final String format;

  VideoOutputConfig({
    this.fps = 30,
    this.width = 720,
    this.height = 1280,
    this.bitrate = 2000000, // 2 Mbps
    this.codec = 'libx264',
    this.format = 'mp4',
  });
}

class VideoOutputGenerator {
  static final VideoOutputGenerator _instance = VideoOutputGenerator._internal();
  factory VideoOutputGenerator() => _instance;
  VideoOutputGenerator._internal();

  /// Generate video from processed frames with overlays
  Future<String> generateVideoFromFrames(
    List<img.Image> frames,
    String originalVideoPath, {
    VideoOutputConfig? config,
    Function(VideoGenerationProgress)? onProgress,
  }) async {
    config ??= VideoOutputConfig();
    
    if (frames.isEmpty) {
      throw Exception('No frames provided for video generation');
    }

    try {
      // Create temporary directory for frame images
      final tempDir = await getTemporaryDirectory();
      final framesDir = Directory('${tempDir.path}/output_frames_${DateTime.now().millisecondsSinceEpoch}');
      
      if (!await framesDir.exists()) {
        await framesDir.create(recursive: true);
      }

      onProgress?.call(VideoGenerationProgress(
        currentFrame: 0,
        totalFrames: frames.length,
        stage: 'Saving frames...',
        progress: 0.0,
      ));

      // Save frames as individual images
      final frameFiles = <String>[];
      for (int i = 0; i < frames.length; i++) {
        final frameFile = '${framesDir.path}/frame_${i.toString().padLeft(6, '0')}.png';
        final pngBytes = img.encodePng(frames[i]);
        await File(frameFile).writeAsBytes(pngBytes);
        frameFiles.add(frameFile);

        // Update progress
        final progress = (i + 1) / frames.length * 0.5; // First 50% for saving frames
        onProgress?.call(VideoGenerationProgress(
          currentFrame: i + 1,
          totalFrames: frames.length,
          stage: 'Saving frames...',
          progress: progress,
        ));
      }

      onProgress?.call(VideoGenerationProgress(
        currentFrame: frames.length,
        totalFrames: frames.length,
        stage: 'Generating video...',
        progress: 0.5,
      ));

      // Generate output video path
      final outputDir = await _getOutputDirectory();
      final outputFileName = 'analyzed_video_${DateTime.now().millisecondsSinceEpoch}.${config.format}';
      final outputPath = path.join(outputDir.path, outputFileName);

      // Create video from frames using FFmpeg
      await _createVideoFromFrames(
        framesDir.path,
        outputPath,
        config,
        onProgress,
      );

      // Clean up temporary files
      await framesDir.delete(recursive: true);

      onProgress?.call(VideoGenerationProgress(
        currentFrame: frames.length,
        totalFrames: frames.length,
        stage: 'Complete',
        progress: 1.0,
      ));

      return outputPath;
    } catch (e) {
      throw Exception('Failed to generate video: $e');
    }
  }

  /// Create video from frame images using FFmpeg
  Future<void> _createVideoFromFrames(
    String framesDir,
    String outputPath,
    VideoOutputConfig config,
    Function(VideoGenerationProgress)? onProgress,
  ) async {
    try {
      // FFmpeg command to create video from images
      final inputPattern = '$framesDir/frame_%06d.png';
      final command = [
        '-framerate', config.fps.toString(),
        '-i', inputPattern,
        '-c:v', config.codec,
        '-b:v', config.bitrate.toString(),
        '-pix_fmt', 'yuv420p',
        '-y', // Overwrite output file
        outputPath,
      ].join(' ');

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      if (!ReturnCode.isSuccess(returnCode)) {
        final logs = await session.getLogs();
        final errorMessage = logs.map((log) => log.getMessage()).join('\n');
        throw Exception('FFmpeg video creation failed: $errorMessage');
      }

      // Verify output file exists
      if (!await File(outputPath).exists()) {
        throw Exception('Output video file was not created');
      }
    } catch (e) {
      throw Exception('Failed to create video from frames: $e');
    }
  }

  /// Generate video with audio from original video
  Future<String> generateVideoWithAudio(
    String processedVideoPath,
    String originalVideoPath, {
    Function(VideoGenerationProgress)? onProgress,
  }) async {
    try {
      onProgress?.call(VideoGenerationProgress(
        currentFrame: 0,
        totalFrames: 1,
        stage: 'Adding audio...',
        progress: 0.0,
      ));

      final outputDir = await _getOutputDirectory();
      final outputFileName = 'analyzed_with_audio_${DateTime.now().millisecondsSinceEpoch}.mp4';
      final outputPath = path.join(outputDir.path, outputFileName);

      // Extract audio from original video and combine with processed video
      final command = [
        '-i', processedVideoPath,
        '-i', originalVideoPath,
        '-c:v', 'copy',
        '-c:a', 'aac',
        '-map', '0:v:0',
        '-map', '1:a:0?', // Optional audio stream
        '-shortest',
        '-y',
        outputPath,
      ].join(' ');

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      if (!ReturnCode.isSuccess(returnCode)) {
        // If audio combination fails, return the processed video without audio
        return processedVideoPath;
      }

      onProgress?.call(VideoGenerationProgress(
        currentFrame: 1,
        totalFrames: 1,
        stage: 'Complete',
        progress: 1.0,
      ));

      return outputPath;
    } catch (e) {
      // Return processed video without audio if audio combination fails
      return processedVideoPath;
    }
  }

  /// Compress video to reduce file size
  Future<String> compressVideo(
    String inputPath, {
    int targetBitrate = 1000000, // 1 Mbps
    Function(VideoGenerationProgress)? onProgress,
  }) async {
    try {
      onProgress?.call(VideoGenerationProgress(
        currentFrame: 0,
        totalFrames: 1,
        stage: 'Compressing video...',
        progress: 0.0,
      ));

      final outputDir = await _getOutputDirectory();
      final inputFile = File(inputPath);
      final fileName = path.basenameWithoutExtension(inputFile.path);
      final outputPath = path.join(outputDir.path, '${fileName}_compressed.mp4');

      final command = [
        '-i', inputPath,
        '-c:v', 'libx264',
        '-b:v', targetBitrate.toString(),
        '-c:a', 'aac',
        '-b:a', '128k',
        '-preset', 'medium',
        '-crf', '23',
        '-y',
        outputPath,
      ].join(' ');

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      if (!ReturnCode.isSuccess(returnCode)) {
        throw Exception('Video compression failed');
      }

      onProgress?.call(VideoGenerationProgress(
        currentFrame: 1,
        totalFrames: 1,
        stage: 'Complete',
        progress: 1.0,
      ));

      return outputPath;
    } catch (e) {
      throw Exception('Failed to compress video: $e');
    }
  }

  /// Get or create output directory for processed videos
  Future<Directory> _getOutputDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final outputDir = Directory(path.join(appDir.path, 'fitness_ai_videos'));
    
    if (!await outputDir.exists()) {
      await outputDir.create(recursive: true);
    }
    
    return outputDir;
  }

  /// Get video information
  Future<Map<String, dynamic>> getVideoInfo(String videoPath) async {
    try {
      final command = '-i "$videoPath" -hide_banner';
      final session = await FFmpegKit.execute(command);
      final output = await session.getOutput();
      
      final info = <String, dynamic>{};
      
      if (output != null) {
        // Extract basic video information
        final durationMatch = RegExp(r'Duration:\s*(\d+):(\d+):(\d+)\.(\d+)').firstMatch(output);
        if (durationMatch != null) {
          final hours = int.parse(durationMatch.group(1)!);
          final minutes = int.parse(durationMatch.group(2)!);
          final seconds = int.parse(durationMatch.group(3)!);
          info['duration'] = hours * 3600 + minutes * 60 + seconds;
        }
        
        final resolutionMatch = RegExp(r'(\d+)x(\d+)').firstMatch(output);
        if (resolutionMatch != null) {
          info['width'] = int.parse(resolutionMatch.group(1)!);
          info['height'] = int.parse(resolutionMatch.group(2)!);
        }
        
        final fpsMatch = RegExp(r'(\d+(?:\.\d+)?)\s*fps').firstMatch(output);
        if (fpsMatch != null) {
          info['fps'] = double.parse(fpsMatch.group(1)!);
        }
      }
      
      return info;
    } catch (e) {
      return {};
    }
  }

  /// Clean up temporary files
  Future<void> cleanupTempFiles() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final tempFiles = tempDir.listSync()
          .where((entity) => entity.path.contains('video_frames_') || 
                           entity.path.contains('output_frames_'))
          .toList();
      
      for (final file in tempFiles) {
        if (file is Directory) {
          await file.delete(recursive: true);
        } else {
          await file.delete();
        }
      }
    } catch (e) {
      // Ignore cleanup errors
    }
  }
}
